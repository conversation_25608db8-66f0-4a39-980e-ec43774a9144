#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存优化版本：分批处理Excel文件合并和按大洲拆分
"""

import pandas as pd
import logging
import os
import gc
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 国家到大洲的映射
CONTINENT_MAPPING = {
    'Asia': [
        'Bahrain', 'Cambodia', 'Cyprus', 'East Timor', 'Hong Kong', 'Indonesia', 'Japan', 
        'Jordan', 'Kazakhstan', 'Korea', 'Kuwait', 'Lao', 'Malaysia', 'Maldives', 'Mongolia', 
        'Myanmar', 'Oman', 'Pakistan', 'Palestine', 'Philippines', 'Qatar', 'Saudi Arabia', 
        'Singapore', 'Thailand', 'Turkey', 'UAE', 'Uzbekistan', 'Viet Nam', 'Brunei'
    ],
    'Europe': [
        'Austria', 'Belgium', 'Denmark', 'Finland', 'France', 'Germany', 'Greece', 
        'Hungary', 'Ireland', 'Italy', 'Latvia', 'Malta', 'Netherlands', 'Norway', 
        'Poland', 'Portugal', 'Serbia', 'Slovenia', 'Spain', 'Sweden', 'Switzerland', 
        'UK'
    ],
    'Africa': [
        'Angola', 'Cabo Verde', 'Egypt', 'Gabon', 'Ghana', 'Kenya', 'Liberia', 
        'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco', 
        'Namibia', 'Nigeria', 'Senegal', 'Somalia', 'South Africa', 'Tanzania', 
        'Togo', 'Zimbabwe'
    ],
    'America': [
        'Argentina', 'Brazil', 'Canada', 'Chile', 'Colombia', 'Costa Rica', 
        'Ecuador', 'Jamaica', 'Mexico', 'Peru', 'Suriname', 'USA'
    ],
    'Oceania': [
        'Australia', 'Fiji', 'French Polynesia', 'New Zealand', 'PNG'
    ]
}

def get_continent_by_country(country_name):
    """根据国家名称获取对应的大洲"""
    clean_country = country_name.strip()
    
    # 处理特殊情况
    special_cases = {
        'Hong Kong': 'Hong Kong', 'HongKong': 'Hong Kong',
        'Lao': 'Lao', 'PNG': 'PNG', 'Papua': 'PNG',
        'French Polynesia': 'French Polynesia',
        'Costa Rica': 'Costa Rica', 'Saudi Arabia': 'Saudi Arabia',
        'South Africa': 'South Africa', 'New Zealand': 'New Zealand',
        'East Timor': 'East Timor', 'Cabo': 'Cabo Verde'
    }
    
    for key, value in special_cases.items():
        if key in clean_country:
            clean_country = value
            break
    
    # 在映射中查找国家对应的大洲
    for continent, countries in CONTINENT_MAPPING.items():
        for country in countries:
            if country.lower() in clean_country.lower() or clean_country.lower() in country.lower():
                return continent
    
    logger.warning(f"未找到国家 '{country_name}' 对应的大洲")
    return 'Unknown'

def get_excel_files(directory=".", exclude_patterns=None):
    """获取目录中的Excel文件"""
    if exclude_patterns is None:
        exclude_patterns = ["合并结果", "test_", "~$"]
    
    excel_extensions = ['.xlsx', '.xls']
    excel_files = []
    
    for file_path in Path(directory).iterdir():
        if file_path.is_file() and file_path.suffix.lower() in excel_extensions:
            # 检查是否需要排除
            should_exclude = any(pattern in file_path.name for pattern in exclude_patterns)
            if not should_exclude:
                excel_files.append(file_path)
    
    return excel_files

def process_file_batch(file_batch, batch_num):
    """处理一批文件"""
    logger.info(f"开始处理第 {batch_num} 批文件，包含 {len(file_batch)} 个文件")
    
    continent_writers = {}
    processed_count = 0
    
    try:
        # 为每个大洲创建临时文件写入器
        for continent in CONTINENT_MAPPING.keys():
            temp_file = f"temp_{continent}_batch_{batch_num}.xlsx"
            continent_writers[continent] = {
                'file': temp_file,
                'sheets': []
            }
        
        # 处理每个文件
        for file_path in file_batch:
            try:
                logger.info(f"正在处理文件: {file_path.name}")
                
                # 获取最后一个工作表名称
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names
                if not sheet_names:
                    logger.warning(f"文件 {file_path} 没有工作表")
                    continue
                
                last_sheet_name = sheet_names[-1]
                
                # 读取数据
                df = pd.read_excel(file_path, sheet_name=last_sheet_name)
                
                if df.empty:
                    logger.warning(f"工作表 '{last_sheet_name}' 为空")
                    continue
                
                # 获取大洲
                continent = get_continent_by_country(last_sheet_name)
                
                # 添加大洲列
                df['Continent'] = continent
                
                # 保存到对应大洲的临时文件
                continent_writers[continent]['sheets'].append({
                    'name': last_sheet_name,
                    'data': df
                })
                
                logger.info(f"成功处理 {last_sheet_name} -> {continent}")
                processed_count += 1
                
                # 清理内存
                del df
                gc.collect()
                
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {e}")
                continue
        
        # 将每个大洲的数据写入临时文件
        for continent, info in continent_writers.items():
            if info['sheets']:
                temp_file = info['file']
                with pd.ExcelWriter(temp_file, engine='openpyxl') as writer:
                    for sheet_info in info['sheets']:
                        sheet_info['data'].to_excel(writer, sheet_name=sheet_info['name'], index=False)
                
                logger.info(f"批次 {batch_num} 的 {continent} 数据已保存到 {temp_file}")
                
                # 清理内存
                for sheet_info in info['sheets']:
                    del sheet_info['data']
                gc.collect()
        
        logger.info(f"第 {batch_num} 批处理完成，成功处理 {processed_count} 个文件")
        
    except Exception as e:
        logger.error(f"处理批次 {batch_num} 时出错: {e}")

def merge_temp_files():
    """合并所有临时文件"""
    logger.info("开始合并临时文件...")
    
    # 获取所有临时文件
    temp_files = list(Path(".").glob("temp_*.xlsx"))
    
    if not temp_files:
        logger.error("没有找到临时文件")
        return
    
    # 按大洲分组临时文件
    continent_temp_files = {}
    for temp_file in temp_files:
        for continent in CONTINENT_MAPPING.keys():
            if continent in temp_file.name:
                if continent not in continent_temp_files:
                    continent_temp_files[continent] = []
                continent_temp_files[continent].append(temp_file)
                break
    
    # 为每个大洲合并文件
    for continent, files in continent_temp_files.items():
        if not files:
            continue
            
        logger.info(f"正在合并 {continent} 的 {len(files)} 个临时文件")
        
        final_file = f"合并结果_{continent}.xlsx"
        all_sheets = {}
        
        # 读取所有临时文件的工作表
        for temp_file in files:
            try:
                excel_file = pd.ExcelFile(temp_file)
                for sheet_name in excel_file.sheet_names:
                    # 处理重复的工作表名称
                    unique_name = sheet_name
                    counter = 1
                    while unique_name in all_sheets:
                        unique_name = f"{sheet_name}_{counter}"
                        counter += 1
                    
                    df = pd.read_excel(temp_file, sheet_name=sheet_name)
                    all_sheets[unique_name] = df
                    
                    logger.info(f"读取工作表: {unique_name}")
                
            except Exception as e:
                logger.error(f"读取临时文件 {temp_file} 时出错: {e}")
        
        # 写入最终文件
        if all_sheets:
            with pd.ExcelWriter(final_file, engine='openpyxl') as writer:
                for sheet_name, df in all_sheets.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            logger.info(f"已创建 {continent} 最终文件: {final_file}，包含 {len(all_sheets)} 个工作表")
        
        # 清理内存
        for df in all_sheets.values():
            del df
        gc.collect()
    
    # 删除临时文件
    for temp_file in temp_files:
        try:
            temp_file.unlink()
            logger.info(f"已删除临时文件: {temp_file}")
        except Exception as e:
            logger.warning(f"删除临时文件 {temp_file} 失败: {e}")

def main():
    """主函数"""
    logger.info("开始内存优化的Excel文件处理...")
    
    # 获取所有Excel文件
    excel_files = get_excel_files()
    
    if not excel_files:
        logger.error("没有找到Excel文件")
        return
    
    logger.info(f"找到 {len(excel_files)} 个Excel文件")
    
    # 分批处理文件（每批10个文件）
    batch_size = 10
    total_batches = (len(excel_files) + batch_size - 1) // batch_size
    
    for i in range(0, len(excel_files), batch_size):
        batch_num = i // batch_size + 1
        file_batch = excel_files[i:i + batch_size]
        
        logger.info(f"处理批次 {batch_num}/{total_batches}")
        process_file_batch(file_batch, batch_num)
        
        # 强制垃圾回收
        gc.collect()
    
    # 合并所有临时文件
    merge_temp_files()
    
    logger.info("所有处理完成！")

if __name__ == "__main__":
    main()
