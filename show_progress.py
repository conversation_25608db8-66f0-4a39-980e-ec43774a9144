#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示处理进度的脚本
"""

from pathlib import Path
import time

def show_current_progress():
    """显示当前处理进度"""
    print("Excel文件处理进度报告")
    print("=" * 50)
    
    # 统计临时文件
    temp_files = list(Path(".").glob("temp_*.xlsx"))
    
    # 按批次和大洲分组
    batches = {}
    continents = set()
    
    for temp_file in temp_files:
        parts = temp_file.stem.split('_')
        if len(parts) >= 4:  # temp_Continent_batch_X
            continent = parts[1]
            batch_num = parts[3]
            
            continents.add(continent)
            
            if batch_num not in batches:
                batches[batch_num] = []
            batches[batch_num].append(continent)
    
    print(f"发现的大洲: {', '.join(sorted(continents))}")
    print(f"已完成的批次数: {len(batches)}")
    print()
    
    # 显示每个批次的详情
    for batch_num in sorted(batches.keys(), key=int):
        continents_in_batch = sorted(batches[batch_num])
        print(f"批次 {batch_num}: {', '.join(continents_in_batch)}")
    
    print()
    print(f"总临时文件数: {len(temp_files)}")
    
    # 检查最终结果文件
    result_files = list(Path(".").glob("合并结果_*.xlsx"))
    if result_files:
        print(f"最终结果文件: {len(result_files)} 个")
        for file in sorted(result_files):
            file_size = file.stat().st_size / (1024 * 1024)  # MB
            print(f"  {file.name} ({file_size:.1f} MB)")
    else:
        print("最终结果文件: 尚未生成")
    
    print("=" * 50)

if __name__ == "__main__":
    show_current_progress()
