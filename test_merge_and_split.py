#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试版本：合并前5个Excel文件并按大洲拆分
"""

import pandas as pd
import logging
import os
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 国家到大洲的映射
CONTINENT_MAPPING = {
    # 亚洲 (Asia)
    'Asia': [
        'Bahrain', 'Cambodia', 'Cyprus', 'East Timor', 'Hong Kong', 'Indonesia', 'Japan', 
        'Jordan', 'Kazakhstan', 'Korea', 'Kuwait', 'Lao', 'Malaysia', 'Maldives', 'Mongolia', 
        'Myanmar', 'Oman', 'Pakistan', 'Palestine', 'Philippines', 'Qatar', 'Saudi Arabia', 
        'Singapore', 'Thailand', 'Turkey', 'UAE', 'Uzbekistan', 'Viet Nam', 'Brunei'
    ],
    # 欧洲 (Europe)
    'Europe': [
        'Austria', 'Belgium', 'Denmark', 'Finland', 'France', 'Germany', 'Greece', 
        'Hungary', 'Ireland', 'Italy', 'Latvia', 'Malta', 'Netherlands', 'Norway', 
        'Poland', 'Portugal', 'Serbia', 'Slovenia', 'Spain', 'Sweden', 'Switzerland', 
        'UK'
    ],
    # 非洲 (Africa)
    'Africa': [
        'Angola', 'Cabo Verde', 'Egypt', 'Gabon', 'Ghana', 'Kenya', 'Liberia', 
        'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco', 
        'Namibia', 'Nigeria', 'Senegal', 'Somalia', 'South Africa', 'Tanzania', 
        'Togo', 'Zimbabwe'
    ],
    # 美洲 (America)
    'America': [
        'Argentina', 'Brazil', 'Canada', 'Chile', 'Colombia', 'Costa Rica', 
        'Ecuador', 'Jamaica', 'Mexico', 'Peru', 'Suriname', 'USA'
    ],
    # 大洋洲 (Oceania)
    'Oceania': [
        'Australia', 'Fiji', 'French Polynesia', 'New Zealand', 'PNG'
    ]
}

def get_continent_by_country(country_name):
    """根据国家名称获取对应的大洲"""
    clean_country = country_name.strip()
    
    # 处理特殊情况
    special_cases = {
        'Hong Kong': 'Hong Kong',
        'Lao': 'Lao',
        'PNG': 'PNG',
        'Papua': 'PNG',
        'French Polynesia': 'French Polynesia',
        'Costa Rica': 'Costa Rica',
        'Saudi Arabia': 'Saudi Arabia',
        'South Africa': 'South Africa',
        'New Zealand': 'New Zealand',
        'East Timor': 'East Timor',
        'Cabo': 'Cabo Verde'
    }
    
    for key, value in special_cases.items():
        if key in clean_country:
            clean_country = value
            break
    
    # 在映射中查找国家对应的大洲
    for continent, countries in CONTINENT_MAPPING.items():
        for country in countries:
            if country.lower() in clean_country.lower() or clean_country.lower() in country.lower():
                return continent
    
    logger.warning(f"未找到国家 '{country_name}' 对应的大洲")
    return 'Unknown'

def get_excel_files(directory=".", max_files=5):
    """获取目录中的Excel文件"""
    excel_extensions = ['.xlsx', '.xls']
    excel_files = []
    
    for file_path in Path(directory).iterdir():
        if file_path.is_file() and file_path.suffix.lower() in excel_extensions:
            if not file_path.name.startswith("合并结果") and not file_path.name.startswith("test_"):
                excel_files.append(file_path)
    
    return excel_files[:max_files] if max_files > 0 else excel_files

def get_last_worksheet_name(file_path):
    """获取Excel文件的最后一个工作表名称"""
    try:
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        return sheet_names[-1] if sheet_names else None
    except Exception as e:
        logger.error(f"无法读取文件 {file_path} 的工作表名称: {e}")
        return None

def read_last_worksheet(file_path):
    """读取Excel文件的最后一个工作表"""
    try:
        last_sheet_name = get_last_worksheet_name(file_path)
        if last_sheet_name is None:
            return None, None
        
        df = pd.read_excel(file_path, sheet_name=last_sheet_name)
        
        if df.empty:
            logger.warning(f"文件 {file_path} 的工作表 '{last_sheet_name}' 为空")
            return None, last_sheet_name
        
        logger.info(f"成功读取文件 {file_path} 的工作表 '{last_sheet_name}'，包含 {len(df)} 行数据")
        return df, last_sheet_name
        
    except Exception as e:
        logger.error(f"无法读取文件 {file_path}: {e}")
        return None, None

def merge_and_split_by_continent():
    """合并Excel文件并按大洲拆分"""
    logger.info("开始测试：合并前5个Excel文件并按大洲拆分...")
    
    # 获取前5个Excel文件
    excel_files = get_excel_files(max_files=5)
    
    if not excel_files:
        logger.error("没有找到Excel文件")
        return
    
    logger.info(f"找到 {len(excel_files)} 个Excel文件进行测试")
    
    # 存储所有工作表数据
    sheets_data = {}
    used_sheet_names = set()
    
    # 处理每个Excel文件
    for file_path in excel_files:
        logger.info(f"正在处理文件: {file_path.name}")
        
        df, original_sheet_name = read_last_worksheet(file_path)
        
        if df is not None and original_sheet_name is not None:
            # 生成唯一的工作表名称
            unique_sheet_name = original_sheet_name
            counter = 1
            while unique_sheet_name in used_sheet_names:
                unique_sheet_name = f"{original_sheet_name}_{counter}"
                counter += 1
            
            used_sheet_names.add(unique_sheet_name)
            
            # 获取国家对应的大洲
            continent = get_continent_by_country(unique_sheet_name)
            
            # 添加大洲列
            df['Continent'] = continent
            
            # 存储数据
            sheets_data[unique_sheet_name] = {
                'data': df,
                'continent': continent
            }
            
            logger.info(f"工作表 '{unique_sheet_name}' 归类到 '{continent}' 大洲")
    
    if not sheets_data:
        logger.error("没有成功读取到任何工作表数据")
        return
    
    # 按大洲分组数据
    continent_data = {}
    for sheet_name, info in sheets_data.items():
        continent = info['continent']
        if continent not in continent_data:
            continent_data[continent] = {}
        continent_data[continent][sheet_name] = info['data']
    
    # 创建合并文件
    output_file = "test_合并结果.xlsx"
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for sheet_name, info in sheets_data.items():
            info['data'].to_excel(writer, sheet_name=sheet_name, index=False)
            logger.info(f"已写入合并文件中的工作表: {sheet_name}")
    
    logger.info(f"合并完成！结果已保存到 '{output_file}'")
    
    # 为每个大洲创建单独的Excel文件
    for continent, sheets in continent_data.items():
        continent_file = f"test_合并结果_{continent}.xlsx"
        
        with pd.ExcelWriter(continent_file, engine='openpyxl') as writer:
            for sheet_name, df in sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                logger.info(f"已写入 {continent} 文件中的工作表: {sheet_name}")
        
        logger.info(f"已创建 {continent} 大洲的文件: {continent_file}")
    
    logger.info("按大洲拆分完成！")
    
    # 显示拆分结果统计
    print("\n按大洲拆分结果:")
    for continent, sheets in continent_data.items():
        print(f"{continent}: {len(sheets)} 个国家/地区")
        for sheet_name in sheets.keys():
            print(f"  - {sheet_name}")
        print()

if __name__ == "__main__":
    merge_and_split_by_continent()
