#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控处理进度的脚本
"""

import os
import time
from pathlib import Path

def monitor_progress():
    """监控处理进度"""
    print("监控Excel文件处理进度...")
    print("=" * 50)
    
    while True:
        # 检查临时文件
        temp_files = list(Path(".").glob("temp_*.xlsx"))
        
        # 检查最终结果文件
        result_files = list(Path(".").glob("合并结果_*.xlsx"))
        
        print(f"\r临时文件: {len(temp_files)} | 完成文件: {len(result_files)}", end="", flush=True)
        
        # 如果有5个大洲的文件都完成了，说明处理完成
        if len(result_files) >= 5:
            print("\n处理完成！")
            break
            
        time.sleep(5)

def show_final_results():
    """显示最终结果"""
    print("\n" + "=" * 50)
    print("最终结果文件:")
    
    result_files = list(Path(".").glob("合并结果_*.xlsx"))
    
    for file in sorted(result_files):
        file_size = file.stat().st_size / (1024 * 1024)  # MB
        print(f"  {file.name} ({file_size:.1f} MB)")
    
    print(f"\n总共生成了 {len(result_files)} 个大洲文件")

if __name__ == "__main__":
    try:
        monitor_progress()
        show_final_results()
    except KeyboardInterrupt:
        print("\n监控已停止")
