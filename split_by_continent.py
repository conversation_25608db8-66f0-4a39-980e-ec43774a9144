#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按大洲拆分Excel文件脚本
为合并的Excel文件添加大洲列并按大洲拆分为单独的文件
"""

import pandas as pd
import logging
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 国家到大洲的映射
CONTINENT_MAPPING = {
    # 亚洲 (Asia)
    'Asia': [
        'Bahrain', 'Cambodia', 'Cyprus', 'East Timor', 'Hong Kong', 'Indonesia', 'Japan', 
        'Jordan', 'Kazakhstan', 'Korea', 'Kuwait', 'Lao', 'Malaysia', 'Maldives', 'Mongolia', 
        'Myanmar', 'Oman', 'Pakistan', 'Palestine', 'Philippines', 'Qatar', 'Saudi Arabia', 
        'Singapore', 'Thailand', 'Turkey', 'UAE', 'Uzbekistan', 'Viet Nam', 'Brunei'
    ],
    # 欧洲 (Europe)
    'Europe': [
        'Austria', 'Belgium', 'Denmark', 'Finland', 'France', 'Germany', 'Greece', 
        'Hungary', 'Ireland', 'Italy', 'Latvia', 'Malta', 'Netherlands', 'Norway', 
        'Poland', 'Portugal', 'Serbia', 'Slovenia', 'Spain', 'Sweden', 'Switzerland', 
        'UK'
    ],
    # 非洲 (Africa)
    'Africa': [
        'Angola', 'Cabo Verde', 'Egypt', 'Gabon', 'Ghana', 'Kenya', 'Liberia', 
        'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco', 
        'Namibia', 'Nigeria', 'Senegal', 'Somalia', 'South Africa', 'Tanzania', 
        'Togo', 'Zimbabwe'
    ],
    # 美洲 (America)
    'America': [
        'Argentina', 'Brazil', 'Canada', 'Chile', 'Colombia', 'Costa Rica', 
        'Ecuador', 'Jamaica', 'Mexico', 'Peru', 'Suriname', 'USA'
    ],
    # 大洋洲 (Oceania)
    'Oceania': [
        'Australia', 'Fiji', 'French Polynesia', 'New Zealand', 'PNG'
    ]
}

def get_continent_by_country(country_name):
    """根据国家名称获取对应的大洲"""
    # 清理国家名称，去除多余的空格和特殊字符
    clean_country = country_name.strip()
    
    # 处理一些特殊情况
    if 'Hong Kong' in clean_country:
        clean_country = 'Hong Kong'
    elif 'Lao' in clean_country:
        clean_country = 'Lao'
    elif 'PNG' in clean_country or 'Papua' in clean_country:
        clean_country = 'PNG'
    elif 'French Polynesia' in clean_country:
        clean_country = 'French Polynesia'
    elif 'Costa Rica' in clean_country:
        clean_country = 'Costa Rica'
    elif 'Saudi Arabia' in clean_country:
        clean_country = 'Saudi Arabia'
    elif 'South Africa' in clean_country:
        clean_country = 'South Africa'
    elif 'New Zealand' in clean_country:
        clean_country = 'New Zealand'
    elif 'East Timor' in clean_country:
        clean_country = 'East Timor'
    elif 'Cabo' in clean_country:
        clean_country = 'Cabo Verde'
    
    # 在映射中查找国家对应的大洲
    for continent, countries in CONTINENT_MAPPING.items():
        for country in countries:
            if country.lower() in clean_country.lower() or clean_country.lower() in country.lower():
                return continent
    
    # 如果没有找到，返回未知
    logger.warning(f"未找到国家 '{country_name}' 对应的大洲")
    return 'Unknown'

def add_continent_column_and_split():
    """为合并文件添加大洲列并按大洲拆分"""
    merged_file = "合并结果.xlsx"
    
    if not os.path.exists(merged_file):
        logger.error(f"合并文件 '{merged_file}' 不存在，请先运行合并操作")
        return
    
    logger.info("开始为合并文件添加大洲列并按大洲拆分...")
    
    try:
        # 读取合并的Excel文件
        excel_file = pd.ExcelFile(merged_file)
        continent_data = {}
        
        # 处理每个工作表
        for sheet_name in excel_file.sheet_names:
            logger.info(f"正在处理工作表: {sheet_name}")
            
            # 读取工作表数据
            df = pd.read_excel(merged_file, sheet_name=sheet_name)
            
            # 获取国家对应的大洲
            continent = get_continent_by_country(sheet_name)
            
            # 添加大洲列
            df['Continent'] = continent
            
            # 按大洲分组存储数据
            if continent not in continent_data:
                continent_data[continent] = {}
            
            continent_data[continent][sheet_name] = df
            
            logger.info(f"工作表 '{sheet_name}' 归类到 '{continent}' 大洲")
        
        # 为每个大洲创建单独的Excel文件
        for continent, sheets in continent_data.items():
            output_file = f"合并结果_{continent}.xlsx"
            
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                for sheet_name, df in sheets.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    logger.info(f"已写入 {continent} 文件中的工作表: {sheet_name}")
            
            logger.info(f"已创建 {continent} 大洲的文件: {output_file}")
        
        logger.info("按大洲拆分完成！")
        
        # 显示拆分结果统计
        print("\n按大洲拆分结果:")
        for continent, sheets in continent_data.items():
            print(f"{continent}: {len(sheets)} 个国家/地区")
            for sheet_name in sheets.keys():
                print(f"  - {sheet_name}")
            print()
                
    except Exception as e:
        logger.error(f"处理合并文件时出错: {e}")

if __name__ == "__main__":
    add_continent_column_and_split()
