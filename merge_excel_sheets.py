#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel工作表合并脚本
将当前目录中所有Excel文件的最后一个工作表合并到一个新的Excel文件中
"""

import os
import pandas as pd
from pathlib import Path
import logging
from collections import defaultdict
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 国家到大洲的映射
CONTINENT_MAPPING = {
    # 亚洲 (Asia)
    'Asia': [
        'Bahrain', 'Cambodia', 'Cyprus', 'East Timor', 'Hong Kong', 'Indonesia', 'Japan',
        'Jordan', 'Kazakhstan', 'Korea', 'Kuwait', 'Lao', 'Malaysia', 'Maldives', 'Mongolia',
        'Myanmar', 'Oman', 'Pakistan', 'Palestine', 'Philippines', 'Qatar', 'Saudi Arabia',
        'Singapore', 'Thailand', 'Turkey', 'UAE', 'Uzbekistan', 'Viet Nam', 'Brunei'
    ],
    # 欧洲 (Europe)
    'Europe': [
        'Austria', 'Belgium', 'Denmark', 'Finland', 'France', 'Germany', 'Greece',
        'Hungary', 'Ireland', 'Italy', 'Latvia', 'Malta', 'Netherlands', 'Norway',
        'Poland', 'Portugal', 'Serbia', 'Slovenia', 'Spain', 'Sweden', 'Switzerland',
        'UK'
    ],
    # 非洲 (Africa)
    'Africa': [
        'Angola', 'Cabo Verde', 'Egypt', 'Gabon', 'Ghana', 'Kenya', 'Liberia',
        'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco',
        'Namibia', 'Nigeria', 'Senegal', 'Somalia', 'South Africa', 'Tanzania',
        'Togo', 'Zimbabwe'
    ],
    # 美洲 (America)
    'America': [
        'Argentina', 'Brazil', 'Canada', 'Chile', 'Colombia', 'Costa Rica',
        'Ecuador', 'Jamaica', 'Mexico', 'Peru', 'Suriname', 'USA'
    ],
    # 大洋洲 (Oceania)
    'Oceania': [
        'Australia', 'Fiji', 'French Polynesia', 'New Zealand', 'PNG'
    ]
}

def get_continent_by_country(country_name):
    """根据国家名称获取对应的大洲"""
    # 清理国家名称，去除多余的空格和特殊字符
    clean_country = country_name.strip()

    # 处理一些特殊情况
    if 'Hong Kong' in clean_country:
        clean_country = 'Hong Kong'
    elif 'Lao' in clean_country:
        clean_country = 'Lao'
    elif 'PNG' in clean_country or 'Papua' in clean_country:
        clean_country = 'PNG'
    elif 'French Polynesia' in clean_country:
        clean_country = 'French Polynesia'
    elif 'Costa Rica' in clean_country:
        clean_country = 'Costa Rica'
    elif 'Saudi Arabia' in clean_country:
        clean_country = 'Saudi Arabia'
    elif 'South Africa' in clean_country:
        clean_country = 'South Africa'
    elif 'New Zealand' in clean_country:
        clean_country = 'New Zealand'
    elif 'East Timor' in clean_country:
        clean_country = 'East Timor'
    elif 'Cabo' in clean_country:
        clean_country = 'Cabo Verde'

    # 在映射中查找国家对应的大洲
    for continent, countries in CONTINENT_MAPPING.items():
        for country in countries:
            if country.lower() in clean_country.lower() or clean_country.lower() in country.lower():
                return continent

    # 如果没有找到，返回未知
    logger.warning(f"未找到国家 '{country_name}' 对应的大洲")
    return 'Unknown'

def get_excel_files(directory="."):
    """获取目录中的所有Excel文件"""
    excel_extensions = ['.xlsx', '.xls']
    excel_files = []

    for file_path in Path(directory).iterdir():
        if file_path.is_file() and file_path.suffix.lower() in excel_extensions:
            # 排除合并结果文件
            if file_path.name != "合并结果.xlsx" and not file_path.name.startswith("合并结果_"):
                excel_files.append(file_path)

    return excel_files

def get_last_worksheet_name(file_path):
    """获取Excel文件的最后一个工作表名称"""
    try:
        # 使用pandas读取Excel文件的所有工作表名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        if sheet_names:
            return sheet_names[-1]  # 返回最后一个工作表名称
        else:
            logger.warning(f"文件 {file_path} 没有工作表")
            return None
    except Exception as e:
        logger.error(f"无法读取文件 {file_path} 的工作表名称: {e}")
        return None

def read_last_worksheet(file_path):
    """读取Excel文件的最后一个工作表"""
    try:
        # 获取最后一个工作表名称
        last_sheet_name = get_last_worksheet_name(file_path)
        if last_sheet_name is None:
            return None, None
        
        # 读取最后一个工作表
        df = pd.read_excel(file_path, sheet_name=last_sheet_name)
        
        # 检查工作表是否为空
        if df.empty:
            logger.warning(f"文件 {file_path} 的工作表 '{last_sheet_name}' 为空")
            return None, last_sheet_name
        
        logger.info(f"成功读取文件 {file_path} 的工作表 '{last_sheet_name}'，包含 {len(df)} 行数据")
        return df, last_sheet_name
        
    except Exception as e:
        logger.error(f"无法读取文件 {file_path}: {e}")
        return None, None

def generate_unique_sheet_name(base_name, existing_names):
    """生成唯一的工作表名称"""
    if base_name not in existing_names:
        return base_name
    
    counter = 1
    while f"{base_name}_{counter}" in existing_names:
        counter += 1
    
    return f"{base_name}_{counter}"

def merge_excel_sheets(max_files=5):
    """主函数：合并所有Excel文件的最后一个工作表"""
    logger.info("开始合并Excel文件的最后一个工作表...")

    # 获取当前目录中的所有Excel文件
    excel_files = get_excel_files()

    if not excel_files:
        logger.error("当前目录中没有找到Excel文件")
        return

    # 限制处理的文件数量用于测试
    if max_files > 0:
        excel_files = excel_files[:max_files]
        logger.info(f"找到 {len(get_excel_files())} 个Excel文件，限制处理前 {len(excel_files)} 个文件进行测试")
    else:
        logger.info(f"找到 {len(excel_files)} 个Excel文件，开始处理所有文件")
    
    # 存储所有工作表数据
    sheets_data = {}
    used_sheet_names = set()
    
    # 处理每个Excel文件
    for file_path in excel_files:
        logger.info(f"正在处理文件: {file_path.name}")
        
        # 读取最后一个工作表
        df, original_sheet_name = read_last_worksheet(file_path)
        
        if df is not None and original_sheet_name is not None:
            # 生成唯一的工作表名称
            unique_sheet_name = generate_unique_sheet_name(original_sheet_name, used_sheet_names)
            used_sheet_names.add(unique_sheet_name)
            
            # 存储数据
            sheets_data[unique_sheet_name] = df
            
            if unique_sheet_name != original_sheet_name:
                logger.info(f"工作表名称从 '{original_sheet_name}' 重命名为 '{unique_sheet_name}' 以避免重复")
    
    # 检查是否有数据需要合并
    if not sheets_data:
        logger.error("没有成功读取到任何工作表数据")
        return
    
    # 创建新的Excel文件
    output_file = "合并结果.xlsx"
    
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            for sheet_name, df in sheets_data.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                logger.info(f"已写入工作表: {sheet_name}")
        
        logger.info(f"合并完成！结果已保存到 '{output_file}'")
        logger.info(f"总共合并了 {len(sheets_data)} 个工作表")
        
        # 显示合并的工作表列表
        print("\n合并的工作表列表:")
        for i, sheet_name in enumerate(sheets_data.keys(), 1):
            print(f"{i}. {sheet_name}")
            
    except Exception as e:
        logger.error(f"保存合并结果时出错: {e}")

def add_continent_column_and_split():
    """为合并文件添加大洲列并按大洲拆分"""
    merged_file = "合并结果.xlsx"

    if not os.path.exists(merged_file):
        logger.error(f"合并文件 '{merged_file}' 不存在，请先运行合并操作")
        return

    logger.info("开始为合并文件添加大洲列并按大洲拆分...")

    try:
        # 读取合并的Excel文件
        excel_file = pd.ExcelFile(merged_file)
        continent_data = {}

        # 处理每个工作表
        for sheet_name in excel_file.sheet_names:
            logger.info(f"正在处理工作表: {sheet_name}")

            # 读取工作表数据
            df = pd.read_excel(merged_file, sheet_name=sheet_name)

            # 获取国家对应的大洲
            continent = get_continent_by_country(sheet_name)

            # 添加大洲列
            df['Continent'] = continent

            # 按大洲分组存储数据
            if continent not in continent_data:
                continent_data[continent] = {}

            continent_data[continent][sheet_name] = df

            logger.info(f"工作表 '{sheet_name}' 归类到 '{continent}' 大洲")

        # 为每个大洲创建单独的Excel文件
        for continent, sheets in continent_data.items():
            output_file = f"合并结果_{continent}.xlsx"

            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                for sheet_name, df in sheets.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    logger.info(f"已写入 {continent} 文件中的工作表: {sheet_name}")

            logger.info(f"已创建 {continent} 大洲的文件: {output_file}")

        logger.info("按大洲拆分完成！")

        # 显示拆分结果统计
        print("\n按大洲拆分结果:")
        for continent, sheets in continent_data.items():
            print(f"{continent}: {len(sheets)} 个国家/地区")
            for sheet_name in sheets.keys():
                print(f"  - {sheet_name}")
            print()

    except Exception as e:
        logger.error(f"处理合并文件时出错: {e}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "split":
        # 只执行拆分操作
        add_continent_column_and_split()
    else:
        # 先合并，再拆分
        merge_excel_sheets(max_files=0)
        print("\n" + "="*50)
        add_continent_column_and_split()
