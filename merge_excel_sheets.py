#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel工作表合并脚本
将当前目录中所有Excel文件的最后一个工作表合并到一个新的Excel文件中
"""

import os
import pandas as pd
from pathlib import Path
import logging
from collections import defaultdict

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_excel_files(directory="."):
    """获取目录中的所有Excel文件"""
    excel_extensions = ['.xlsx', '.xls']
    excel_files = []
    
    for file_path in Path(directory).iterdir():
        if file_path.is_file() and file_path.suffix.lower() in excel_extensions:
            excel_files.append(file_path)
    
    return excel_files

def get_last_worksheet_name(file_path):
    """获取Excel文件的最后一个工作表名称"""
    try:
        # 使用pandas读取Excel文件的所有工作表名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        if sheet_names:
            return sheet_names[-1]  # 返回最后一个工作表名称
        else:
            logger.warning(f"文件 {file_path} 没有工作表")
            return None
    except Exception as e:
        logger.error(f"无法读取文件 {file_path} 的工作表名称: {e}")
        return None

def read_last_worksheet(file_path):
    """读取Excel文件的最后一个工作表"""
    try:
        # 获取最后一个工作表名称
        last_sheet_name = get_last_worksheet_name(file_path)
        if last_sheet_name is None:
            return None, None
        
        # 读取最后一个工作表
        df = pd.read_excel(file_path, sheet_name=last_sheet_name)
        
        # 检查工作表是否为空
        if df.empty:
            logger.warning(f"文件 {file_path} 的工作表 '{last_sheet_name}' 为空")
            return None, last_sheet_name
        
        logger.info(f"成功读取文件 {file_path} 的工作表 '{last_sheet_name}'，包含 {len(df)} 行数据")
        return df, last_sheet_name
        
    except Exception as e:
        logger.error(f"无法读取文件 {file_path}: {e}")
        return None, None

def generate_unique_sheet_name(base_name, existing_names):
    """生成唯一的工作表名称"""
    if base_name not in existing_names:
        return base_name
    
    counter = 1
    while f"{base_name}_{counter}" in existing_names:
        counter += 1
    
    return f"{base_name}_{counter}"

def merge_excel_sheets(max_files=5):
    """主函数：合并所有Excel文件的最后一个工作表"""
    logger.info("开始合并Excel文件的最后一个工作表...")

    # 获取当前目录中的所有Excel文件
    excel_files = get_excel_files()

    if not excel_files:
        logger.error("当前目录中没有找到Excel文件")
        return

    # 限制处理的文件数量用于测试
    if max_files > 0:
        excel_files = excel_files[:max_files]
        logger.info(f"找到 {len(get_excel_files())} 个Excel文件，限制处理前 {len(excel_files)} 个文件进行测试")
    else:
        logger.info(f"找到 {len(excel_files)} 个Excel文件，开始处理所有文件")
    
    # 存储所有工作表数据
    sheets_data = {}
    used_sheet_names = set()
    
    # 处理每个Excel文件
    for file_path in excel_files:
        logger.info(f"正在处理文件: {file_path.name}")
        
        # 读取最后一个工作表
        df, original_sheet_name = read_last_worksheet(file_path)
        
        if df is not None and original_sheet_name is not None:
            # 生成唯一的工作表名称
            unique_sheet_name = generate_unique_sheet_name(original_sheet_name, used_sheet_names)
            used_sheet_names.add(unique_sheet_name)
            
            # 存储数据
            sheets_data[unique_sheet_name] = df
            
            if unique_sheet_name != original_sheet_name:
                logger.info(f"工作表名称从 '{original_sheet_name}' 重命名为 '{unique_sheet_name}' 以避免重复")
    
    # 检查是否有数据需要合并
    if not sheets_data:
        logger.error("没有成功读取到任何工作表数据")
        return
    
    # 创建新的Excel文件
    output_file = "合并结果.xlsx"
    
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            for sheet_name, df in sheets_data.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                logger.info(f"已写入工作表: {sheet_name}")
        
        logger.info(f"合并完成！结果已保存到 '{output_file}'")
        logger.info(f"总共合并了 {len(sheets_data)} 个工作表")
        
        # 显示合并的工作表列表
        print("\n合并的工作表列表:")
        for i, sheet_name in enumerate(sheets_data.keys(), 1):
            print(f"{i}. {sheet_name}")
            
    except Exception as e:
        logger.error(f"保存合并结果时出错: {e}")

if __name__ == "__main__":
    # 处理所有文件（max_files=0表示处理所有文件）
    merge_excel_sheets(max_files=0)
